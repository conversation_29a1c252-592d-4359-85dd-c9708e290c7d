import { Box, Button, Grid2 } from "@mui/material";
import React from "react";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import Modal from "src/modules/Common/Modal/Modal";
import { z } from "zod";

const benefitsSchema = z.object({
  name: z.string().nonempty({
    message: "Benefit Name is required",
  }),
  benefitPlan: z.enum(["National Pension Scheme", "Non Taxable Deductions"], {
    errorMap: () => ({ message: "Benefit Plan is required" }),
  }),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  title: string;
};

const AddEditBenefits: React.FC<Props> = ({ isOpen, onClose, title }) => {
  const form = useAppForm({
    defaultValues: {
      name: "",
      benefitPlan: "National Pension Scheme",
    },
    validators: {
      onSubmit: benefitsSchema,
    },
    onSubmit: (props) => {
      console.log(props);
    },
  });

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      showBackButton
      title={title}
      actions={
        <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting, state.isPristine]}>
          {([canSubmit, isSubmitting, isPristine]) => {
            return (
              <Box display="flex" p={2} gap={1} justifyContent="flex-end">
                <Button variant="outlined" onClick={() => onClose()}>
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  type="submit"
                  disabled={!canSubmit || isPristine || isSubmitting}
                  onClick={form.handleSubmit}
                >
                  Save
                </Button>
              </Box>
            );
          }}
        </form.Subscribe>
      }
    >
      <Grid2 container spacing={2}>
        <Grid2 size={12}>
          <form.AppField name="benefitPlan">
            {(field: any) => (
              <field.EffiRadioGroup
                label="Benefit Plan"
                layout="horizontal"
                required
                options={[
                  { label: "National Pension Scheme", value: "National Pension Scheme" },
                  { label: "Non Taxable Deductions", value: "Non Taxable Deductions" },
                ]}
              />
            )}
          </form.AppField>
        </Grid2>
        <Grid2 size={12}>
          <form.AppField name="name">
            {(field: any) => <field.EffiTextField label="Benefit Name" required placeholder="Enter benefit name" />}
          </form.AppField>
        </Grid2>
      </Grid2>
    </Modal>
  );
};

export default AddEditBenefits;
