import { Checkbox } from "@mui/material";
import React from "react";
import DataTable from "src/modules/Common/Table/DataTable";
import { CompensationComponent } from "src/services/api_definitions/payroll.service";
import { CompensationComponentContext } from "../CompensationComponents";

const ViewBenefits: React.FC<{
  benefits: CompensationComponent[];
}> = ({ benefits }) => {
  const compensationComponentContext = React.useContext(CompensationComponentContext);

  return (
    <DataTable
      data={benefits}
      state={{
        showSkeletons: compensationComponentContext.isLoading,
      }}
      columns={[
        {
          accessorKey: "name",
          header: "Name",
        },
        {
          accessorKey: "taxable",
          header: "Is Taxable",
          Cell: ({ cell }) => <Checkbox checked={cell.getValue<boolean>()} disabled />,
        },
        {
          accessorKey: "pro_rated",
          header: "Is Pro-rated",
          Cell: ({ cell }) => <Checkbox checked={cell.getValue<boolean>()} disabled />,
        },
        {
          accessorKey: "mandatory",
          header: "Is Mandatory",
          Cell: ({ cell }) => <Checkbox checked={cell.getValue<boolean>()} disabled />,
        },
        {
          accessorKey: "system_defined",
          header: "Is System Defined",
          Cell: ({ cell }) => <Checkbox checked={cell.getValue<boolean>()} disabled />,
        },
        {
          accessorKey: "pay_type",
          header: "Pay Type",
        },
      ]}
    />
  );
};

export default ViewBenefits;
