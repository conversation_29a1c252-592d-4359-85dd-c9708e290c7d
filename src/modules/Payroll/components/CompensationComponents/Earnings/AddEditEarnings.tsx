import { Box, Button, Grid2, Paper, Typography } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import { useMutation } from "@tanstack/react-query";
import React, { useMemo } from "react";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import Modal from "src/modules/Common/Modal/Modal";
import { CompensationComponent } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import { z } from "zod";
import { CompensationComponentContext } from "../CompensationComponents";
import EffiDynamicForm from "src/modules/Common/Form/components/EffiDynamicForm";

const earningsSchema = z.object({
  name: z.string().nonempty({
    message: "Name is required",
  }),
  calculationType: z.enum(["Flat", "Percentage"]),
  amount: z.number().gt(0, {
    message: "Amount should be greater than 0",
  }),
  payType: z.enum(["Fixed", "Variable"]),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  selectedRow: CompensationComponent | null;
};

const earningConfigurations: {
  key: 'consider_for_esi' | 'include_in_fbp' | 'consider_for_epf';
  label: string;
  value: boolean | string;
  selected_option?: string;
  options?: { label: string; value: string }[];
}[] = [{
  key: "consider_for_esi",
  label: "Consider for ESI",
  value: false,
}, {
  key: "include_in_fbp",
  label: "Include in FBP",
  value: true,
}, {
  key: "consider_for_epf",
  label: "Consider for EPF",
  value: false,
  selected_option: "Restricted",
  options: [
    { label: "Always", value: "ALWAYS" },
    { label: "Restricted", value: "RESTRICTED" },
  ],
},]

const AddEditEarnings: React.FC<Props> = ({ isOpen, onClose, title, selectedRow }) => {
  const { refetch } = React.useContext(CompensationComponentContext);
  const isEditMode = useMemo(() => !!selectedRow, [selectedRow]);

  const createEarningMutation = useMutation({
    mutationKey: ["create-earning"],
    mutationFn: async (payload: Partial<CompensationComponent>) => payrollService.createCompensationComponent(payload),
    onSuccess: () => {
      refetch();
      onClose();
    },
  });

  const getValue = (key: string, defaultValue: boolean | string) => {
    if (isEditMode) {
      return selectedRow?.attributes?.find((eachAttribute) => Reflect.has(eachAttribute, key));
    }
    return defaultValue;
  };

  const form = useAppForm({
    defaultValues: {
      name: "",
      calculationType: "Flat",
      amount: 0,
      payType: "Fixed",
      configurations: earningConfigurations.map((eachConfig) => ({
        ...eachConfig,
        value: getValue(eachConfig.key, eachConfig.value),
        selected_option: eachConfig.selected_option,
      })),
    },
    validators: {
      onSubmit: earningsSchema,
    },
    onSubmit: ({ value }) => {
      console.log({ value });
      const request: Partial<CompensationComponent> = {
        name: value.name,
        component_type: "Earning",
        pay_type: value.payType,
        formula: {
          value: value.amount,
          calculation_type: value.calculationType,
        },
      };
      createEarningMutation.mutate(request);
    },
  });

  const calculationType = useStore(form.store, (state) => state.values.calculationType);

  const inputProps = earningConfigurations.map((eachConfig, index) => ({
    fieldProps: {
      name: eachConfig.key === "consider_for_epf" ? `configurations.${index}.selected_option` : `configurations.${index}.value`,
      mode: "array",
    },
    formProps: {
      type: eachConfig.key === "consider_for_epf" ? "radio-group" : "checkbox",
      label: eachConfig.label,
      required: false,
      options: eachConfig.options || [],
    },
    containerProps: {
      size: 6,
    },
  }));

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      showBackButton
      title={title}
      actions={
        <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting, state.isPristine]}>
          {([canSubmit, isSubmitting, isPristine]) => {
            return (
              <Box display="flex" p={2} gap={1} justifyContent="flex-end">
                <Button variant="outlined" onClick={() => onClose()}>
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  type="submit"
                  disabled={!canSubmit || isPristine || isSubmitting}
                  onClick={form.handleSubmit}
                >
                  Save
                </Button>
              </Box>
            );
          }}
        </form.Subscribe>
      }
    >
      <Grid2 container spacing={2}>
        <Grid2 size={12}>
          <form.AppField name="name">
            {(field: any) => <field.EffiTextField label="Earning Name" required />}
          </form.AppField>
        </Grid2>
        <Grid2 size={6} gap={1}>
          <Box component={Paper} elevation={2} p={2} height={200}>
            <form.AppField name="calculationType">
              {(field: any) => (
                <field.EffiRadioGroup
                  label="Calculation Type"
                  layout="horizontal"
                  required
                  options={[
                    { label: "Flat Amount", value: "Flat" },
                    { label: "Percentage of CTC", value: "Percentage" },
                  ]}
                />
              )}
            </form.AppField>
            <form.AppField name="amount">
              {(field: any) => {
                if (calculationType === "Percentage") {
                  return (
                    <field.EffiPercentageField
                      label="Amount"
                      required
                      endHelperText="of CTC"
                      placeholder="Enter percentage"
                    />
                  );
                }
                return <field.EffiCurrency label="Amount" required currency="INR" placeholder="Enter amount" />;
              }}
            </form.AppField>
          </Box>
        </Grid2>
        <Grid2 size={6} gap={1}>
          <Box component={Paper} elevation={2} p={2} height={200}>
            <form.AppField name="payType">
              {(field: any) => (
                <field.EffiRadioGroup
                  label="Pay Type"
                  layout="vertical"
                  required
                  options={[
                    { label: "Fixed Pay", value: "Fixed", subLabel: "(Fixed amount paid at the end of every month)" },
                    { label: "Variable Pay", value: "Variable", subLabel: "(Variable amount paid any payroll)" },
                  ]}
                />
              )}
            </form.AppField>
          </Box>
        </Grid2>
        <Grid2 size={12}>
          <Typography>Configurations</Typography>
          <EffiDynamicForm form={form} inputFields={inputProps} />
        </Grid2>
      </Grid2>
    </Modal>
  );
};

export default AddEditEarnings;
