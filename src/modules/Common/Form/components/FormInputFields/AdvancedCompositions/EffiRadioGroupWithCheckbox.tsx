import { Box, Checkbox, FormControl, FormControlLabel, Radio, RadioGroup, Typography } from "@mui/material";
import React from "react";
import { CustomInputLabel } from "src/modules/Common/FormInputs/CustomInputLabel";
import { useFieldContext } from "../../effiFormContext";

type RadioOption = {
  value: string;
  label: string;
  subLabel?: string;
  disabled?: boolean;
};

type EffiRadioGroupWithCheckboxProps = {
  label: string;
  required?: boolean;
  options: RadioOption[];
  layout?: "horizontal" | "vertical";
  size?: "small" | "medium";
};

const EffiRadioGroupWithCheckbox: React.FC<EffiRadioGroupWithCheckboxProps> = ({
  label,
  required,
  options,
  layout = "vertical",
  size = "small",
}) => {
  const field = useFieldContext();
  const [isCheckboxChecked, setIsCheckboxChecked] = React.useState(false);

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsCheckboxChecked(event.target.checked);
  };

  const handleRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    field.handleChange(event.target.value);
  };

  return (
    <Box display="flex" flexDirection="column">
      
    </Box>
  );
};

export default EffiRadioGroupWithCheckbox;
