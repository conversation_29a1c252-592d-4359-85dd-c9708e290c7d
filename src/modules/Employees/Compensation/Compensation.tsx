import { Add, Close } from "@mui/icons-material";
import { Box, Button, Divider, Icon<PERSON>utton } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useMemo, useImperativeHandle } from "react";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import { PayrollTemplateV2 } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import { formatDateToDayMonthYear } from "src/utils/dateUtils";
import { z } from "zod";
import { INPUT_FIELDS } from "../config/EmploymentDetails";
import { FormDataType, StepperComponentProps } from "../types/FormDataTypes";
import CompensationItem from "./CompensationItem";
import FixedTemplateFormComponents from "./FixedTemplateFormComponents";

export const FormulaSchema = z.object({
  value: z.union([z.number(), z.string()]),
  code: z.union([z.null(), z.string()]),
  display_name: z.union([z.null(), z.string()]),
  calculation_type: z.string(),
});
export type Formula = z.infer<typeof FormulaSchema>;

export const CompensationComponentSchema = z.object({
  id: z.string(),
  name: z.string().optional(),
  code: z.string(),
  currency: z.string(),
  formula: FormulaSchema,
  mandatory: z.boolean(),
  taxable: z.boolean(),
  system_defined: z.boolean(),
  pro_rated: z.boolean(),
  pay_type: z.string(),
  calculation_type: z.string(),
  component_type: z.string(),
  sort_order: z.null(),
});
export type CompensationComponent = z.infer<typeof CompensationComponentSchema>;

export const ComponentSchema = z.object({
  compensation_component: CompensationComponentSchema,
  amount: z.number(),
});
export type Component = z.infer<typeof ComponentSchema>;

export const TEmployeePayrollSchema = z.object({
  effective_from: z.string(),
  effective_to: z.null(),
  ctc: z.number(),
  components: z.array(ComponentSchema),
  name: z.string().optional(),
});

export type TEmployeePayroll = z.infer<typeof TEmployeePayrollSchema>;

type Props = StepperComponentProps & {
  formData?: {
    current_compensation: TEmployeePayroll;
    next_compensation: TEmployeePayroll;
  };
  disbaleFields?: string[];
  onFormComplete: (form: FormDataType[], isFormSubmit?: boolean, isSaveDraft?: boolean) => void;
  disabledInputFields?: string[][];
  disableDelete?: boolean[];
  initialFormState?: {
    current_compensation: TEmployeePayroll;
    next_compensation: TEmployeePayroll;
  };
  globalFormData: any;
};

enum NextCompensationViewModes {
  CREATE = "CREATE",
}

enum NextCompensationModes {
  READ_ONLY = "READ_ONLY",
  EDITABLE = "EDITABLE",
}

const compensationValidators = z.object({
  next_compensation: z
    .object({
      ctc: z.number().min(1).gt(0),
      name: z.string().nullish(),
      effective_from: z.string(),
      components: z.array(
        z.object({
          compensation_component: z.object({
            id: z.string(),
            name: z.string(),
          }),
          amount: z.number().optional(),
        }),
      ),
    })
    .nullish(),
  current_compensation: z
    .object({
      ctc: z.number().min(1).gt(0),
      effective_from: z.string(),
      name: z.string().nullish(),
      components: z.array(
        z.object({
          compensation_component: z.object({
            id: z.string(),
            name: z.string(),
          }),
          amount: z.number().optional(),
        }),
      ),
    })
    .nullish(),
});

const Compensation: React.FC<Props> = ({
  formActionButton,
  onFormComplete,
  formData,
  isViewOnlyMode,
  setDisableNext,
  initialFormState,
  globalFormData,
}) => {
  console.log({ formData, initialFormState, globalFormData });
  const [preview, setPreview] = React.useState<any>({
    current_compensation: null,
    next_compensation: null,
  });
  const isNextComp = !!formData?.next_compensation || !!initialFormState?.next_compensation;
  const [nextCompensationViewModes, setNextCompensationViewModes] = React.useState<NextCompensationViewModes | null>(
    isNextComp ? NextCompensationViewModes.CREATE : null,
  );

  // Track whether next_compensation is in read-only mode (when pre-populated) or editable mode
  const [nextCompensationMode, setNextCompensationMode] = React.useState<NextCompensationModes>(
    !!formData?.next_compensation || !!initialFormState?.next_compensation
      ? NextCompensationModes.READ_ONLY
      : NextCompensationModes.EDITABLE,
  );
  const { data: templates } = useQuery({
    queryKey: ["get-all-employee-templates"],
    queryFn: async () =>
      await payrollService.getTemplateDetails({
        business_unit: globalFormData?.employementDetails?.[0]?.[INPUT_FIELDS.BUSINESS],
        department: globalFormData?.employementDetails?.[0]?.[INPUT_FIELDS.DEPARTMENT],
        job_title: globalFormData?.employementDetails?.[0]?.[INPUT_FIELDS.JOB_TITLE],
        employee_type: globalFormData?.employementDetails?.[0]?.employee_type,
        country: globalFormData?.employementDetails?.[0]?.["location.country"],
        work_role: globalFormData?.employementDetails?.[0]?.work_role_name,
      }),
  });

  const defaultFormState = useMemo(() => {
    return {
      current_compensation: formData?.current_compensation
        ? formData?.current_compensation
        : initialFormState?.current_compensation,
      next_compensation: formData?.next_compensation
        ? formData?.next_compensation
        : initialFormState?.next_compensation,
    };
  }, [formData]);

  const form = useAppForm({
    defaultValues: defaultFormState,
    validators: {
      onChange: compensationValidators,
    },
  });

  const getRequestStruct = () => {
    if (
      !initialFormState?.current_compensation ||
      (initialFormState?.current_compensation && !nextCompensationViewModes)
    ) {
      const components =
        form.getFieldValue("current_compensation.components") || formData?.current_compensation?.components;
      return {
        current_compensation: {
          ...initialFormState?.current_compensation,
          name: form.getFieldValue("current_compensation.name") || formData?.current_compensation?.name || null,
          effective_date:
            form.getFieldValue("current_compensation.effective_from") || formData?.current_compensation?.effective_from,
          effective_from:
            form.getFieldValue("current_compensation.effective_from") || formData?.current_compensation?.effective_from,
          ctc: form.getFieldValue("current_compensation.ctc") || formData?.current_compensation?.ctc,
          components: components?.map((component) => ({
            ...component,
            amount:
              preview?.["current_compensation"]?.[component?.compensation_component?.name] || component?.amount || 0,
            formula_used: component?.compensation_component?.formula,
          })),
        },
        next_compensation: null,
      };
    }

    const components =
      form.getFieldValue("current_compensation.components") || formData?.current_compensation?.components;
    const nextComponents =
      form.getFieldValue("next_compensation.components") || formData?.next_compensation?.components;
    return {
      current_compensation: {
        ...initialFormState?.current_compensation,
        name: form.getFieldValue("current_compensation.name") || formData?.current_compensation?.name || null,
        effective_date:
          form.getFieldValue("current_compensation.effective_from") || formData?.current_compensation?.effective_from,
        effective_from:
          form.getFieldValue("current_compensation.effective_from") || formData?.current_compensation?.effective_from,
        ctc: form.getFieldValue("current_compensation.ctc") || formData?.current_compensation?.ctc,
        components: components?.map((component) => ({
          ...component,
          amount:
            preview?.["current_compensation"]?.[component?.compensation_component?.name] || component?.amount || 0,
          formula_used: component?.compensation_component?.formula,
        })),
      },
      next_compensation: {
        ...initialFormState?.next_compensation,
        name: form.getFieldValue("next_compensation.name") || formData?.next_compensation?.name || null,
        effective_date:
          form.getFieldValue("next_compensation.effective_from") || formData?.next_compensation?.effective_from,
        effective_from:
          form.getFieldValue("next_compensation.effective_from") || formData?.next_compensation?.effective_from,
        ctc: form.getFieldValue("next_compensation.ctc") || formData?.next_compensation?.ctc,
        components: nextComponents?.map((component) => ({
          ...component,
          amount: preview?.["next_compensation"]?.[component?.compensation_component?.name] || component?.amount || 0,
          formula_used: component?.compensation_component?.formula,
        })),
        amount: form.getFieldValue("next_compensation.ctc") || formData?.next_compensation?.ctc || null,
      },
    };
  };

  useImperativeHandle(formActionButton, () => ({
    next: (isFormSubmit, isSaveDraft) => {
      onFormComplete(getRequestStruct() as unknown as FormDataType[], isFormSubmit, isSaveDraft);
    },
  }));

  const getDisplayDates = (isCurrentCompensation: boolean, state: PayrollTemplateV2) => {
    if (isCurrentCompensation) {
      return `(Effective from ${formatDateToDayMonthYear(state?.effective_from)} ${`${state?.effective_to ? `to ${formatDateToDayMonthYear(state?.effective_to)}` : ""}`})`;
    }
    return state?.effective_from ? `(Effective from ${formatDateToDayMonthYear(state?.effective_from)})` : "";
  };

  const handleRemoveNextCompensation = () => {
    // Clear the next_compensation data
    form.setFieldValue("next_compensation", undefined);
    // Switch to editable mode
    setNextCompensationMode(NextCompensationModes.EDITABLE);
    // Hide the next compensation section
    setNextCompensationViewModes(null);
  };

  return (
    <Box display="flex" flexDirection="column" gap={2} sx={{ margin: "8px 0px" }}>
      <Box display="flex" flexDirection="column" gap={2}>
        <ContentHeader
          title="Current Compensation"
          subtitle={getDisplayDates(true, initialFormState?.current_compensation as unknown as PayrollTemplateV2)}
        />
        <Divider />
        {!initialFormState?.current_compensation && (
          <FixedTemplateFormComponents form={form} isCurrentCompensation templates={templates as PayrollTemplateV2[]} />
        )}
        <CompensationItem
          form={form}
          isCurrentCompensation
          templates={templates as PayrollTemplateV2[]}
          isReadOnly={isViewOnlyMode || !!initialFormState?.current_compensation}
          preview={preview["current_compensation"]}
          setPreview={setPreview}
        />
      </Box>
      {(!isViewOnlyMode || !!formData?.next_compensation) && (
        <Box visibility={nextCompensationViewModes !== NextCompensationViewModes.CREATE ? "visible" : "hidden"}>
          <Button
            variant="outlined"
            onClick={() => {
              setNextCompensationViewModes(NextCompensationViewModes.CREATE);
              setNextCompensationMode(NextCompensationModes.EDITABLE);
            }}
            endIcon={<Add />}
          >
            Next Compensation
          </Button>
        </Box>
      )}
      {nextCompensationViewModes === NextCompensationViewModes.CREATE && (
        <Box display="flex" flexDirection="column" gap={2}>
          <ContentHeader
            title="Next Compensation"
            subtitle={getDisplayDates(false, initialFormState?.next_compensation as unknown as PayrollTemplateV2)}
            actions={
              isViewOnlyMode ? null : (
                <IconButton onClick={handleRemoveNextCompensation}>
                  <Close />
                </IconButton>
              )
            }
          />
          <Divider />
          {(nextCompensationMode === NextCompensationModes.EDITABLE || !form.getFieldValue("next_compensation")) && (
            <FixedTemplateFormComponents
              form={form}
              isCurrentCompensation={false}
              templates={templates as PayrollTemplateV2[]}
              isReadOnlyMode={nextCompensationMode === NextCompensationModes.READ_ONLY}
            />
          )}
          {!isViewOnlyMode &&
            nextCompensationMode === NextCompensationModes.READ_ONLY &&
            form.getFieldValue("next_compensation") && (
              <Box display="flex" flexDirection="column" gap={1}>
                {/* Show effective date field as editable in read-only mode */}
                <form.AppField name="next_compensation.effective_from">
                  {(field: any) => (
                    <field.EffiDate
                      label="Effective Date"
                      minDate={
                        form.getFieldValue("current_compensation.effective_from")
                          ? new Date(form.getFieldValue("current_compensation.effective_from") as string)
                          : undefined
                      }
                    />
                  )}
                </form.AppField>
              </Box>
            )}
          <CompensationItem
            isReadOnly={
              isViewOnlyMode ||
              (nextCompensationMode === NextCompensationModes.READ_ONLY && !!form.getFieldValue("next_compensation"))
            }
            form={form}
            isCurrentCompensation={false}
            templates={templates as PayrollTemplateV2[]}
            preview={preview["next_compensation"]}
            setPreview={setPreview}
          />
        </Box>
      )}
      <form.Subscribe selector={(state) => [state.canSubmit, state.errorMap]}>
        {([canSubmit, errorMap]) => {
          console.log({ errorMap });
          setDisableNext?.(!canSubmit);
          return null;
        }}
      </form.Subscribe>
    </Box>
  );
};

export default Compensation;
