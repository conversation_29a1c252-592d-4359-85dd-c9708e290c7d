import { ExpandMore } from "@mui/icons-material";
import { Accordion, AccordionDetails, AccordionSummary } from "@mui/material";
import { format } from "date-fns";
import React from "react";
import { useMasterData } from "src/customHooks/useMasterData";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import NoData from "src/modules/Dashboard/component/QuickViews/components/NoDataScreens/NoData";
import { Country } from "src/services/api_definitions/location.service";
import { PayrollTemplateV2 } from "src/services/api_definitions/payroll.service";
import EmployeeCompensation from "./EmployeeCompensation";

interface Props {
  compensations?: Pick<
    PayrollTemplateV2,
    "components" | "effective_date" | "template_name" | "effective_from" | "effective_to" | "ctc"
  >[];
  residentCountry: string;
}

const getCompensationTitles = (payroll: PayrollTemplateV2, isCurrent = false) => {
  if (isCurrent) {
    return `Current (Effective from ${format(payroll?.effective_from, "dd MMM yyyy")})`;
  }
  if (!payroll?.effective_to) {
    return `Previous (Effective from ${format(payroll?.effective_from, "dd MMM yyyy")})`;
  }
  return `Previous (From ${format(payroll?.effective_from, "dd MMM yyyy")} to ${format(payroll?.effective_to, "dd MMM yyyy")})`;
};

const EmployeeCompensations: React.FC<Props> = ({ compensations = [], residentCountry }) => {
  const { data: countries } = useMasterData<Country>("Country");
  const formatLocale = countries?.find((country: Country) => country.name === residentCountry)?.code;

  if (!compensations || compensations?.length === 0) {
    <NoData title="No compensations found" />;
  }

  return (
    <>
      {[...compensations]?.map((payroll, index) => (
        <Accordion
          defaultExpanded={index === 0}
          key={`${payroll.template_name}-${index}`}
          slotProps={{ transition: { unmountOnExit: true } }}
        >
          <AccordionSummary expandIcon={<ExpandMore />} aria-controls="panel1-content" id="panel1-header">
            <ContentHeader title={getCompensationTitles(payroll as PayrollTemplateV2, index === 0)} />
          </AccordionSummary>
          <AccordionDetails>
            <EmployeeCompensation
              compensation={payroll?.components || []}
              locale={formatLocale}
              currency={payroll?.components?.[0]?.compensation_component?.currency}
              ctc={payroll?.ctc}
            />
          </AccordionDetails>
        </Accordion>
      ))}
    </>
  );
};

export default EmployeeCompensations;
